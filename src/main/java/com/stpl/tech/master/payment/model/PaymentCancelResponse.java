package com.stpl.tech.master.payment.model;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PaymentCancelResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Common fields for all payment partners
    private PaymentPartnerType paymentPartnerType;
    private String transactionId;
    private String partnerTransactionId;
    private String partnerOrderId;
    private String message;
    private boolean success;
    private String responseCode;
    private String responseMessage;
    
    // Transaction details
    private BigDecimal amount;
    private String merchantId;
    private String terminalId;
    private String cancelTime;
    
    // Pine Labs specific fields
    private Long plutusTransactionReferenceId;
    
    // Paytm specific fields
    private String paytmMid;
    private String paytmTid;
    private String merchantTransactionId;
    
    // PayPhi specific fields
    private String storeCode;
    private String referenceNo;
    private String invoiceNo;
    
    // Status tracking
    private String errorCode;
    private String errorMessage;

}
