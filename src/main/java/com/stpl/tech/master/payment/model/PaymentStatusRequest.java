package com.stpl.tech.master.payment.model;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PaymentStatusRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Common fields for all payment partners
    private PaymentPartnerType paymentPartnerType;
    private String transactionId;
    private String partnerTransactionId;
    private String partnerOrderId;
    private BigDecimal amount;
    private String merchantId;
    private String terminalId;
    
    // Pine Labs specific fields
    private String securityToken;
    private String clientId;
    private String storeId;
    private Long plutusTransactionReferenceId;
    
    // Paytm specific fields
    private String paytmMid;
    private String paytmTid;
    private String merchantKey;
    private String merchantTransactionId;
    
    // PayPhi specific fields
    private String aggregatorId;
    private String referenceNo;
    private String invoiceNo;

}
