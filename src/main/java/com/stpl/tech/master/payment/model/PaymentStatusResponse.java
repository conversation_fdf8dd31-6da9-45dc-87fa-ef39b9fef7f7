package com.stpl.tech.master.payment.model;

import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.PineLabsAdditionalInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PaymentStatusResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // Common fields for all payment partners
    private PaymentPartnerType paymentPartnerType;
    private String transactionId;
    private String partnerTransactionId;
    private String partnerOrderId;
    private BigDecimal amount;
    private boolean success;
    private String responseCode;
    private String responseMessage;
    
    // Pine Labs specific fields
    private Long plutusTransactionReferenceId;
    private List<PineLabsAdditionalInfo> transactionData;
    
    // Paytm specific fields
    private String bankTransactionId;
    private String gatewayName;
    private String bankName;
    
    // PayPhi specific fields
    private String referenceNo;
    
    // Status tracking
    private String errorCode;
    private String errorMessage;

}
