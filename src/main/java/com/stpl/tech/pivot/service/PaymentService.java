package com.stpl.tech.pivot.service;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.master.payment.model.PaymentCancelRequest;
import com.stpl.tech.master.payment.model.PaymentCancelResponse;
import com.stpl.tech.master.payment.model.PaymentStatusRequest;
import com.stpl.tech.master.payment.model.PaymentStatusResponse;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;

import java.util.Map;

public interface PaymentService {

    PaytmEdcCreateRequest initiateTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymEdc) throws Exception;

    OrderPaymentDetail validateOrderPaymentDetail(String transactionId);

    PaytmEDCStatusResponse updateTransactionRequest(PaytmEdcStatusRequest request, PaymentPartnerType patymEdc) throws Exception;

    PayPhiEdcCreateRequest initiatePayphiTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymEdc) throws Exception;

    PayPhiStatusRequest updatePayPhiTransactionRequest(PayPhiStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception;

    PaytmDQRCreateRequest initiateDqrTransactionRequest(OrderPaymentRequest order, PaymentPartnerType patymDqr) throws Exception;
    PaytmDQRStatusResponse updateDqrTransactionRequest(PaytmDQRStatusRequest request, PaymentPartnerType paytmDqr) throws Exception;
    void updateTransactionStatus(Object response) throws Exception;

    OrderPaymentResponse initiatePaymentTransactionRequest(OrderPaymentRequest order) throws Exception;

    PaymentStatusResponse updatePaymentTransactionRequest(PaymentStatusRequest request, PaymentPartnerType paymentPartnerType) throws Exception;

    PaymentCancelResponse cancelPaymentTransaction(PaymentCancelRequest request) throws Exception;

}
