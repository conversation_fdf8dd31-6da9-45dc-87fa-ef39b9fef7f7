package com.stpl.tech.pivot.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.dao.OrderPaymentDetailRepository;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.pivot.domain.PineLabsCancelRequest;
import com.stpl.tech.pivot.domain.PineLabsCancelResponse;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;
import com.stpl.tech.master.payment.model.PaymentCancelRequest;
import com.stpl.tech.master.payment.model.PaymentCancelResponse;
import com.stpl.tech.master.payment.model.PaymentStatusRequest;
import com.stpl.tech.master.payment.model.PaymentStatusResponse;
import com.stpl.tech.pivot.domain.mapper.PineLabsResponseMapper;
import com.stpl.tech.pivot.exceptions.BaseException;
import com.stpl.tech.pivot.properties.PineLabsProperties;
import com.stpl.tech.pivot.service.PineLabsPaymentService;
import com.stpl.tech.pivot.service.WebClientService;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

@Service
@Log4j2
public class PineLabsPaymentServiceImpl implements PineLabsPaymentService {

    @Autowired
    private WebClientService webClientService;
    
    @Autowired
    private PineLabsProperties pineLabsProperties;
    
    @Autowired
    private OrderPaymentDetailRepository orderPaymentDetailRepository;

    @Autowired
    private PineLabsResponseMapper pineLabsResponseMapper;

    public OrderPaymentResponse initiateEdcTransaction (OrderPaymentRequest orderPaymentRequest) throws Exception {
        log.info("Initiating Pine Labs EDC transaction for order: {}", orderPaymentRequest.getGenerateOrderId());

        PineLabsEdcCreateRequest initiateRequest = pineLabsResponseMapper.createPineLabsEDCRequest(orderPaymentRequest);

        String requestJson = new Gson().toJson(initiateRequest);
        log.info("Pine Labs EDC Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getUploadTransactionApi(),
            requestJson
        );
        log.info("Pine Labs EDC Response: {}", response);

        PineLabsEdcCreateResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsEdcCreateResponse.class);
        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }
        pineLabsResponse.setResponseTime(ApplicationUtils.getCurrentTimeISTString());

        if (!pineLabsResponse.isSuccess()) {
            throw new BaseException("Pine Labs Payment Failed: " + pineLabsResponse.getResponseMessage());
        }

        OrderPaymentDetailEntity orderPaymentDetail = pineLabsResponseMapper.saveOrderPaymentDetailForEDC(orderPaymentRequest, initiateRequest, pineLabsResponse);

        orderPaymentDetailRepository.save(orderPaymentDetail);
        log.info("Pine Labs EDC payment detail saved for order: {}", initiateRequest.getTransactionNumber());

        return pineLabsResponseMapper.mapToOrderPaymentResponse(
            orderPaymentRequest,
            initiateRequest ,
            pineLabsResponse,
            PaymentPartnerType.PINE_LABS_EDC
        );
    }

    public OrderPaymentResponse initiateDqrTransaction(OrderPaymentRequest orderPaymentRequest) throws Exception {
        log.info("Initiating Pine Labs DQR transaction for order: {}", orderPaymentRequest);

        PineLabsDQRCreateRequest initiateRequest = pineLabsResponseMapper.createPineLabsDQRRequest(orderPaymentRequest);

        String requestJson = new Gson().toJson(initiateRequest);
        log.info("Pine Labs DQR Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getUploadTransactionApi(),
            requestJson
        );
        log.info("Pine Labs DQR Response: {}", response);

        PineLabsDQRCreateResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsDQRCreateResponse.class);
        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }
        pineLabsResponse.setResponseTime(ApplicationUtils.getCurrentTimeISTString());

        if (!pineLabsResponse.isSuccess()) {
            throw new BaseException("Pine Labs DQR Payment Failed: " + pineLabsResponse.getResponseMessage());
        }

        OrderPaymentDetailEntity orderPaymentDetail = pineLabsResponseMapper.saveOrderPaymentDetailForDQR(orderPaymentRequest, pineLabsResponse, initiateRequest);

        orderPaymentDetailRepository.save(orderPaymentDetail);
        log.info("Pine Labs DQR payment detail saved for order: {}", initiateRequest.getTransactionNumber());

        return pineLabsResponseMapper.mapToOrderPaymentResponse(
            orderPaymentRequest,
            initiateRequest,
            pineLabsResponse,
            PaymentPartnerType.PINE_LABS_DQR
        );
    }

    @Override
    public PaymentStatusResponse updatePayment(PaymentStatusRequest request) throws Exception {
        log.info("Checking payment status for transaction: {}", request.getTransactionId());

        PineLabsStatusRequest pineLabsRequest = pineLabsResponseMapper.mapToPineLabsStatusRequest(request);

        String requestJson = new Gson().toJson(pineLabsRequest);
        log.info("Pine Labs Status Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR  + pineLabsProperties.getGetStatusApi(),
            requestJson
        );
        log.info("Pine Labs Status Response: {}", response);

        PineLabsStatusResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsStatusResponse.class);

        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }

        updateOrderPaymentDetailStatus(request.getPlutusTransactionReferenceId(), pineLabsResponse);

        return pineLabsResponseMapper.mapToPaymentStatusResponse(
                pineLabsResponse,
                request.getPaymentPartnerType(),
                request.getTransactionId(),
                request.getAmount()
        );
    }

    public PaymentCancelResponse cancelTransaction(PaymentCancelRequest request) throws Exception {
        log.info("Cancelling payment transaction: {}", request.getTransactionId());
        PineLabsCancelRequest pineLabsRequest = pineLabsResponseMapper.mapToPineLabsCancelRequest(request);

        String requestJson = new Gson().toJson(pineLabsRequest);
        log.info("Pine Labs Cancel Request: {}", requestJson);

        String response = webClientService.postRequest(
            pineLabsProperties.getBaseUrl() + ApplicationConstant.SEPERATOR + pineLabsProperties.getCancelTransactionApi(),
            requestJson
        );
        log.info("Pine Labs Cancel Response: {}", response);

        PineLabsCancelResponse pineLabsResponse = ApplicationUtils.parseResponse(response, PineLabsCancelResponse.class);

        if (Objects.isNull(pineLabsResponse)) {
            throw new BaseException("Pine Labs Server Not Responding");
        }

        if (pineLabsResponse.isSuccess()) {
            updateOrderPaymentDetailCancellation(request.getPlutusTransactionReferenceId());
        }

        return pineLabsResponseMapper.mapToPaymentCancelResponse(
                pineLabsResponse,
                request
        );

    }

    private void updateOrderPaymentDetailStatus(Long plutusTransactionReferenceId, PineLabsStatusResponse statusResponse) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
                .findByPartnerOrderId(plutusTransactionReferenceId.toString());
        
        if (orderPaymentDetail != null) {
            orderPaymentDetail.setPaymentStatus(statusResponse.getResponseMessage());
            orderPaymentDetail.setUpdateTime(new Date());
            
            if (statusResponse.isApproved()) {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
                orderPaymentDetail.setResponseTime(new Date());
            } else if (statusResponse.isUploaded()) {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
            } else {
                orderPaymentDetail.setRequestStatus(statusResponse.getResponseMessage());
                orderPaymentDetail.setFailureReason(statusResponse.getResponseMessage());
                orderPaymentDetail.setFailureTime(new Date());
            }
            
            orderPaymentDetailRepository.save(orderPaymentDetail);
            log.info("Pine Labs payment status updated for PTRN: {}", plutusTransactionReferenceId);
        }
    }

    private void updateOrderPaymentDetailCancellation(Long plutusTransactionReferenceId) {
        OrderPaymentDetailEntity orderPaymentDetail = orderPaymentDetailRepository
                .findByPartnerOrderId(plutusTransactionReferenceId.toString());
        
        if (orderPaymentDetail != null) {
            orderPaymentDetail.setRequestStatus(PaymentRequestStatus.CANCELLED.name());
            orderPaymentDetail.setPaymentStatus(PaymentRequestStatus.CANCELLED.name());
            orderPaymentDetail.setCancellationTime(new Date());
            orderPaymentDetail.setCancelledBy(ApplicationConstant.SYSTEM);
            
            orderPaymentDetailRepository.save(orderPaymentDetail);
            log.info("Pine Labs payment cancelled for PTRN: {}", plutusTransactionReferenceId);
        }
    }
}
