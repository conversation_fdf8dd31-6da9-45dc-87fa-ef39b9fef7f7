package com.stpl.tech.pivot.controller;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.domain.OrderPaymentDetail;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateRequest;
import com.stpl.tech.pivot.domain.PayPhiEdcCreateResponse;
import com.stpl.tech.pivot.domain.PayPhiStatusRequest;
import com.stpl.tech.master.payment.model.PaymentCancelRequest;
import com.stpl.tech.master.payment.model.PaymentCancelResponse;
import com.stpl.tech.master.payment.model.PaymentStatusRequest;
import com.stpl.tech.master.payment.model.PaymentStatusResponse;
import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEDCStatusResponse;
import com.stpl.tech.pivot.domain.PaytmEdcCreateRequest;
import com.stpl.tech.pivot.domain.PaytmEdcStatusRequest;
import com.stpl.tech.pivot.service.PaymentService;
import com.stpl.tech.pivot.utils.ApiConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.stpl.tech.pivot.domain.PaytmDQRStatusResponse;
import com.stpl.tech.pivot.domain.PaytmDQRCreateRequest;
import com.stpl.tech.pivot.domain.PaytmDQRStatusRequest;

import java.util.Map;

@RestController
@Log4j2
@RequestMapping(value = ApiConstants.EDC, produces = MediaType.APPLICATION_JSON_VALUE)
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @PostMapping(value = "/initiate/transaction", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaytmEdcCreateRequest initiateTransactionRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
        return paymentService.initiateTransactionRequest(order, PaymentPartnerType.PATYM_EDC);
    }

    @PostMapping(value = "/update/transaction", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaytmEDCStatusResponse updateTransactionRequest(@RequestBody final PaytmEdcStatusRequest request) throws Exception {
        return paymentService.updateTransactionRequest(request, PaymentPartnerType.PATYM_EDC);
    }

    @GetMapping(value = "/validate/{id}")
    public OrderPaymentDetail validateOrderPaymentDetail(@PathVariable(value = "id") String transactionId) {
        return paymentService.validateOrderPaymentDetail(transactionId);
    }

    @PostMapping(value = "/initiate/dqr-transaction", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaytmDQRCreateRequest initiateDqrTransactionRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
        return paymentService.initiateDqrTransactionRequest(order, PaymentPartnerType.PAYTM_DQR);
    }

    @PostMapping(value = "/initiate/transaction/payphi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PayPhiEdcCreateRequest initiatePayPhiTransactionRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
        return paymentService.initiatePayphiTransactionRequest(order, PaymentPartnerType.PAYPHI_EDC);
    }

    @PostMapping(value = "/update/transaction/payphi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PayPhiStatusRequest updateTransactionRequestPayPhi(@RequestBody final PayPhiStatusRequest request) throws Exception {
        return paymentService.updatePayPhiTransactionRequest(request, PaymentPartnerType.PAYPHI_EDC);
    }

    @PostMapping(value = "/update/dqr-transaction", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaytmDQRStatusResponse updateDqrTransactionRequest(@RequestBody final PaytmDQRStatusRequest request) throws Exception {
        return paymentService.updateDqrTransactionRequest(request, PaymentPartnerType.PAYTM_DQR);
    }
    @GetMapping(value = "/get/final-payment-status",consumes = MediaType.APPLICATION_JSON_VALUE)
    public void getPaymentStatusByWebhookCall(@RequestBody Object response) throws Exception {
        paymentService.updateTransactionStatus(response);
    }

    @PostMapping(value = "/partner-transaction/initiate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public OrderPaymentResponse initiatePaymentTransactionRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
        return paymentService.initiatePaymentTransactionRequest(order);
    }

    @PostMapping(value = "/partner-transaction/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaymentStatusResponse updatePaymentTransactionRequest(@RequestBody final PaymentStatusRequest request) throws Exception {
        return paymentService.updatePaymentTransactionRequest(request, request.getPaymentPartnerType());
    }

    @PostMapping(value = "/transaction/cancel", consumes = MediaType.APPLICATION_JSON_VALUE)
    public PaymentCancelResponse cancelPaymentTransaction(@RequestBody final PaymentCancelRequest request) throws Exception {
        log.info("Cancelling payment transaction: {} with partner: {}",
                request.getTransactionId(), request.getPaymentPartnerType());
        return paymentService.cancelPaymentTransaction(request);
    }

}
