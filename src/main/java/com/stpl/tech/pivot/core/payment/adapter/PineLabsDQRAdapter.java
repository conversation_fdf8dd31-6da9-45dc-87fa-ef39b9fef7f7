package com.stpl.tech.pivot.core.payment.adapter;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatusRequest;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.core.payment.PaymentAdapter;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.pivot.service.PineLabsPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PineLabsDQRAdapter extends PaymentAdapter<OrderPaymentRequest, OrderPaymentResponse> {

    @Autowired
    private PineLabsPaymentService pineLabsPaymentService;

    @Override
    public OrderPaymentResponse createPaymentRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws Exception {

        if (orderPaymentRequest.getPaymentPartnerType() == null) {
            orderPaymentRequest.setPaymentPartnerType(PaymentPartnerType.PINE_LABS_DQR);
        }
        return pineLabsPaymentService.initiateDqrTransaction(orderPaymentRequest);
    }

    @Override
    public Object updatePayment(Object object, boolean skipSignatureVerification) throws Exception {
        PaymentStatusRequest request = (PaymentStatusRequest) object;
        return pineLabsPaymentService.updatePayment(request);
    }
}
