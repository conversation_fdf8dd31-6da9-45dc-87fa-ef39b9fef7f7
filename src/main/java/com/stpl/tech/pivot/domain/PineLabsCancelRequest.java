package com.stpl.tech.pivot.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.stpl.tech.master.payment.model.PaymentRequest;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.TreeMap;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsCancelRequest implements PaymentRequest {

    @NotNull(message = "Merchant ID cannot be null")
    private String merchantId;

    @NotNull(message = "Security Token cannot be null")
    private String securityToken;

    private String clientId;

    private String storeId;

    @NotNull(message = "Plutus Transaction Reference ID cannot be null")
    private Long plutusTransactionReferenceId;

    @NotNull(message = "Amount cannot be null")
    private BigDecimal amount;
    
    private PineLabsCancelResponse pineLabsCancelResponse;

    @Override
    public String getPartnerOrderId() {
        return plutusTransactionReferenceId != null ? plutusTransactionReferenceId.toString() : null;
    }

    @Override
    public TreeMap<String, String> getPersistentAttributes() {
        TreeMap<String, String> attributes = new TreeMap<>();
        attributes.put("MerchantID", merchantId);
        attributes.put("PlutusTransactionReferenceID", plutusTransactionReferenceId.toString());
        attributes.put("Amount", amount.toString());
        return attributes;
    }

    @Override
    public String getStatus() {
        return pineLabsCancelResponse != null ? 
               pineLabsCancelResponse.getResponseMessage() : null;
    }
}
