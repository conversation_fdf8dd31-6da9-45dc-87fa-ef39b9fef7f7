package com.stpl.tech.pivot.domain.mapper;

import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.OrderPaymentResponse;
import com.stpl.tech.master.payment.model.PaymentCancelRequest;
import com.stpl.tech.master.payment.model.PaymentCancelResponse;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import com.stpl.tech.master.payment.model.PaymentStatusRequest;
import com.stpl.tech.master.payment.model.PaymentStatusResponse;
import com.stpl.tech.pivot.core.payment.PaymentPartnerType;
import com.stpl.tech.pivot.data.model.OrderPaymentDetailEntity;
import com.stpl.tech.pivot.domain.DQRCreateResponse;
import com.stpl.tech.pivot.domain.EDCCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsCancelRequest;
import com.stpl.tech.pivot.domain.PineLabsCancelResponse;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsDQRCreateResponse;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateRequest;
import com.stpl.tech.pivot.domain.PineLabsEdcCreateResponse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.stpl.tech.pivot.domain.PineLabsStatusRequest;
import com.stpl.tech.pivot.domain.PineLabsStatusResponse;
import com.stpl.tech.pivot.properties.PineLabsProperties;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import com.stpl.tech.pivot.utils.ApplicationUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PineLabsResponseMapper {

    @Autowired
    PineLabsProperties pineLabsProperties;

    public PineLabsDQRCreateRequest  createPineLabsDQRRequest(OrderPaymentRequest orderPaymentRequest) {
        return PineLabsDQRCreateRequest.builder()
                .transactionNumber(ApplicationUtils.getGeneratePartnerExternalOrderId(
                        ApplicationConstant.KETTLE_ORDER,
                        AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS))
                .sequenceNumber(1)
                .allowedPaymentMode(ApplicationConstant.PINE_LABS_UPI_PAYMENT_MODE)
                .amount(orderPaymentRequest.getPaidAmount().multiply(BigDecimal.valueOf(100)))
                .userId(orderPaymentRequest.getCustomerName())
                .merchantId(orderPaymentRequest.getMerchantId())
                .securityToken(orderPaymentRequest.getAccessToken())
                .clientId(orderPaymentRequest.getClientId())
                .storeId(orderPaymentRequest.getStoreCode())
                .autoCancelDurationInMinutes(pineLabsProperties.getAutoCancelDurationInMinutes())
                .requestTime(ApplicationUtils.getCurrentTimeISTString())
                .build();
    }

    public PineLabsEdcCreateRequest createPineLabsEDCRequest(OrderPaymentRequest orderPaymentRequest) {
        return PineLabsEdcCreateRequest.builder()
                .transactionNumber(ApplicationUtils.getGeneratePartnerExternalOrderId(
                        ApplicationConstant.KETTLE_ORDER,
                        AppConstants.DATE_TIME_FORMATTER_WITH_NO_CHARACTERS))
                .sequenceNumber(1)
                .allowedPaymentMode(ApplicationConstant.PINE_LABS_CARD_PAYMENT_MODE)
                .amount(orderPaymentRequest.getPaidAmount().multiply(BigDecimal.valueOf(100)))
                .userId(orderPaymentRequest.getCustomerName())
                .merchantId(orderPaymentRequest.getMerchantId())
                .securityToken(orderPaymentRequest.getAccessToken())
                .clientId(orderPaymentRequest.getClientId())
                .storeId(orderPaymentRequest.getStoreCode())
                .autoCancelDurationInMinutes(pineLabsProperties.getAutoCancelDurationInMinutes())
                .requestTime(ApplicationUtils.getCurrentTimeISTString())
                .build();
    }

    public OrderPaymentDetailEntity saveOrderPaymentDetailForEDC(OrderPaymentRequest orderPaymentRequest, PineLabsEdcCreateRequest request, PineLabsEdcCreateResponse response) {
        OrderPaymentDetailEntity orderPaymentDetail = new OrderPaymentDetailEntity();
        orderPaymentDetail.setExternalOrderId(request.getTransactionNumber());
        orderPaymentDetail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
        orderPaymentDetail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
        orderPaymentDetail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
        orderPaymentDetail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
        orderPaymentDetail.setPaymentStatus(response.getResponseMessage());
        orderPaymentDetail.setRequestTime(new Date());
        orderPaymentDetail.setPartnerOrderId(response.getPlutusTransactionReferenceId().toString());
        orderPaymentDetail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
        orderPaymentDetail.setContactNumber(orderPaymentRequest.getContactNumber());
        orderPaymentDetail.setCustomerName(orderPaymentRequest.getCustomerName());
        orderPaymentDetail.setCustomerId(orderPaymentRequest.getCustomerId());
        orderPaymentDetail.setCartId(orderPaymentRequest.getCartId());
        orderPaymentDetail.setMerchantId(orderPaymentRequest.getMerchantId());
        return orderPaymentDetail;
    }

    public OrderPaymentDetailEntity saveOrderPaymentDetailForDQR(OrderPaymentRequest orderPaymentRequest, PineLabsDQRCreateResponse response, PineLabsDQRCreateRequest request) {
        OrderPaymentDetailEntity orderPaymentDetail = new OrderPaymentDetailEntity();
        orderPaymentDetail.setExternalOrderId(request.getTransactionNumber());
        orderPaymentDetail.setPaymentModeId(orderPaymentRequest.getPaymentModeId());
        orderPaymentDetail.setPaymentModeName(orderPaymentRequest.getPaymentModeName());
        orderPaymentDetail.setPaymentSource(orderPaymentRequest.getPaymentSource().name());
        orderPaymentDetail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
        orderPaymentDetail.setPaymentStatus(response.getResponseMessage());
        orderPaymentDetail.setRequestTime(AppUtils.getCurrentTimestamp());
        orderPaymentDetail.setPartnerOrderId(response.getPlutusTransactionReferenceId().toString());
        orderPaymentDetail.setTransactionAmount(orderPaymentRequest.getPaidAmount());
        orderPaymentDetail.setContactNumber(orderPaymentRequest.getContactNumber());
        orderPaymentDetail.setCustomerName(orderPaymentRequest.getCustomerName());
        orderPaymentDetail.setCustomerId(orderPaymentRequest.getCustomerId());
        orderPaymentDetail.setCartId(orderPaymentRequest.getCartId());
        orderPaymentDetail.setMerchantId(orderPaymentRequest.getMerchantId());

        return orderPaymentDetail;
    }

    public OrderPaymentResponse mapToOrderPaymentResponse(OrderPaymentRequest orderPaymentRequest,
                                                          PineLabsEdcCreateRequest pineLabsRequest,
                                                          PineLabsEdcCreateResponse pineLabsResponse,
                                                          PaymentPartnerType paymentPartnerType) {
        
        EDCCreateResponse edcResponse = EDCCreateResponse.builder()
                .responseCode(pineLabsResponse.getResponseCode() != null ? 
                             pineLabsResponse.getResponseCode().toString() : null)
                .responseMessage(pineLabsResponse.getResponseMessage())
                .partnerTransactionId(pineLabsResponse.getPlutusTransactionReferenceId() != null ? 
                                    pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .success(pineLabsResponse.isSuccess())
                .plutusTransactionReferenceId(pineLabsResponse.getPlutusTransactionReferenceId())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .amount(orderPaymentRequest.getPaidAmount().toString())
                .merchantId(orderPaymentRequest.getMerchantId())
                .additionalInfo(pineLabsResponse.getAdditionalInfo())
                .build();

        return OrderPaymentResponse.builder()
                .paymentMode(orderPaymentRequest.getPaymentModeId())
                .paymentModeName(orderPaymentRequest.getPaymentModeName())
                .paymentSource(orderPaymentRequest.getPaymentSource().name())
                .merchantId(orderPaymentRequest.getMerchantId())
                .customerName(orderPaymentRequest.getCustomerName())
                .contactNumber(orderPaymentRequest.getContactNumber())
                .customerId(orderPaymentRequest.getCustomerId())
                .cartId(orderPaymentRequest.getCartId())
                .paymentPartnerType(paymentPartnerType)
                .orderId(pineLabsRequest.getTransactionNumber())
                .paidAmount(orderPaymentRequest.getPaidAmount())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .partnerTransactionId(edcResponse.getPartnerTransactionId())
                .partnerOrderId(pineLabsResponse.getPlutusTransactionReferenceId() != null ?
                               pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .status(pineLabsResponse.getResponseMessage())
                .message(pineLabsResponse.getResponseMessage())
                .requestTime(pineLabsRequest.getRequestTime())
                .responseTime(pineLabsResponse.getResponseTime())
                .requestStatus(pineLabsResponse.isSuccess() ? ApplicationConstant.SUCCESSFUL : ApplicationConstant.FAILED)
                .paymentStatus(pineLabsResponse.getResponseMessage())
                .partnerPaymentStatus(pineLabsResponse.getResponseMessage())
                .edcCreateResponse(edcResponse)
                .build();
    }

    public OrderPaymentResponse mapToOrderPaymentResponse(OrderPaymentRequest orderPaymentRequest,
                                                          PineLabsDQRCreateRequest pineLabsRequest,
                                                          PineLabsDQRCreateResponse pineLabsResponse,
                                                          PaymentPartnerType paymentPartnerType) {
        
        DQRCreateResponse dqrResponse = DQRCreateResponse.builder()
                .responseCode(pineLabsResponse.getResponseCode() != null ? 
                             pineLabsResponse.getResponseCode().toString() : null)
                .responseMessage(pineLabsResponse.getResponseMessage())
                .partnerTransactionId(pineLabsResponse.getPlutusTransactionReferenceId() != null ? 
                                    pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .amount(orderPaymentRequest.getPaidAmount().toString())
                .merchantId(orderPaymentRequest.getMerchantId())
                .success(pineLabsResponse.isSuccess())
                .plutusTransactionReferenceId(pineLabsResponse.getPlutusTransactionReferenceId())
                .additionalInfo(pineLabsResponse.getAdditionalInfo())
                .build();

        return OrderPaymentResponse.builder()
                .paymentMode(orderPaymentRequest.getPaymentModeId())
                .paymentModeName(orderPaymentRequest.getPaymentModeName())
                .paymentSource(orderPaymentRequest.getPaymentSource().name())
                .customerName(orderPaymentRequest.getCustomerName())
                .contactNumber(orderPaymentRequest.getContactNumber())
                .customerId(orderPaymentRequest.getCustomerId())
                .cartId(orderPaymentRequest.getCartId())
                .paymentPartnerType(paymentPartnerType)
                .orderId(pineLabsRequest.getTransactionNumber())
                .paidAmount(orderPaymentRequest.getPaidAmount())
                .partnerTransactionId(dqrResponse.getPartnerTransactionId())
                .transactionId(pineLabsRequest.getTransactionNumber())
                .partnerOrderId(pineLabsResponse.getPlutusTransactionReferenceId() != null ?
                               pineLabsResponse.getPlutusTransactionReferenceId().toString() : null)
                .merchantId(orderPaymentRequest.getMerchantId())
                .status(pineLabsResponse.getResponseMessage())
                .message(pineLabsResponse.getResponseMessage())
                .requestTime(pineLabsRequest.getRequestTime())
                .responseTime(pineLabsResponse.getResponseTime())
                .requestStatus(pineLabsResponse.isSuccess()
                        ? ApplicationConstant.SUCCESSFUL : ApplicationConstant.FAILED)
                .paymentStatus(pineLabsResponse.getResponseMessage())
                .partnerPaymentStatus(pineLabsResponse.getResponseMessage())
                .dqrCreateResponse(dqrResponse)
                .build();
    }

    public PineLabsStatusRequest mapToPineLabsStatusRequest(PaymentStatusRequest request) {
        return PineLabsStatusRequest.builder()
                .merchantId(request.getMerchantId())
                .securityToken(request.getSecurityToken())
                .clientId(request.getClientId())
                .storeId(request.getStoreId())
                .plutusTransactionReferenceId(request.getPlutusTransactionReferenceId())
                .build();
    }

    public PaymentStatusResponse mapToPaymentStatusResponse(PineLabsStatusResponse pineLabsResponse,
                                                            PaymentPartnerType paymentPartnerType,
                                                            String transactionId, BigDecimal amount) {
        String partnerTxnIdStr = (Objects.nonNull(pineLabsResponse.getPlutusTransactionReferenceId()))
                ? pineLabsResponse.getPlutusTransactionReferenceId().toString() : null;
        return PaymentStatusResponse.builder()
                .paymentPartnerType(paymentPartnerType)
                .transactionId(transactionId)
                .partnerTransactionId(partnerTxnIdStr)
                .partnerOrderId(partnerTxnIdStr)
                .success(pineLabsResponse.isSuccess())
                .responseCode(pineLabsResponse.getResponseCode().toString())
                .responseMessage(pineLabsResponse.getResponseMessage())
                .plutusTransactionReferenceId(pineLabsResponse.getPlutusTransactionReferenceId())
                .amount(amount)
                .transactionData(pineLabsResponse.getTransactionData())
                .build();
    }

    public PineLabsCancelRequest mapToPineLabsCancelRequest(PaymentCancelRequest request) {
        return PineLabsCancelRequest.builder()
                .merchantId(request.getMerchantId())
                .securityToken(request.getSecurityToken())
                .clientId(request.getClientId())
                .storeId(request.getStoreId())
                .plutusTransactionReferenceId(request.getPlutusTransactionReferenceId())
                .amount(request.getAmount().multiply(BigDecimal.valueOf(100)))
                .build();
    }

    public PaymentCancelResponse mapToPaymentCancelResponse(PineLabsCancelResponse pineLabsResponse,
                                                            PaymentCancelRequest request) {
        String partnerTxnIdStr = (Objects.nonNull(request.getPlutusTransactionReferenceId())) ? request.getPlutusTransactionReferenceId().toString() : null;
        return PaymentCancelResponse.builder()
                .paymentPartnerType(request.getPaymentPartnerType())
                .transactionId(request.getTransactionId())
                .partnerTransactionId(partnerTxnIdStr)
                .partnerOrderId(partnerTxnIdStr)
                .amount(request.getAmount())
                .message(pineLabsResponse.getResponseMessage())
                .success(pineLabsResponse.isSuccess())
                .responseCode(pineLabsResponse.getResponseCode().toString())
                .responseMessage(pineLabsResponse.getResponseMessage())
                .plutusTransactionReferenceId(request.getPlutusTransactionReferenceId())
                .cancelTime(ApplicationUtils.getCurrentTimeISTString())
                .build();
    }

}
