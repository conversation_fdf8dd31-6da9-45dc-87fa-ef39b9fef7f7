package com.stpl.tech.pivot.domain;

import com.google.gson.annotations.SerializedName;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsStatusResponse {

    @SerializedName("ResponseCode")
    private Integer responseCode;
    
    @SerializedName("ResponseMessage")
    private String responseMessage;
    
    @SerializedName("PlutusTransactionReferenceID")
    private Long plutusTransactionReferenceId;
    
    @SerializedName("TransactionData")
    private List<PineLabsAdditionalInfo> transactionData;

    public boolean isSuccess() {
        return ApplicationConstant.PINE_LABS_SUCCESS_CODE.equals(responseCode);
    }
    
    public boolean isApproved() {
        return ApplicationConstant.PINE_LABS_APPROVED.equalsIgnoreCase(responseMessage);
    }
    
    public boolean isUploaded() {
        return ApplicationConstant.PINE_LABS_UPLOADED_CODE.equals(responseCode) &&
               ApplicationConstant.PINE_LABS_UPLOADED.equalsIgnoreCase(responseMessage);
    }
    
    public boolean isVoided() {
        return ApplicationConstant.PINE_LABS_VOIDED_CODE.equals(responseCode) &&
               ApplicationConstant.PINE_LABS_VOIDED.equalsIgnoreCase(responseMessage);
    }
    
    public boolean isUpiInitiated() {
        return ApplicationConstant.PINE_LABS_UPI_INITIATED_CODE.equals(responseCode);
    }
}
