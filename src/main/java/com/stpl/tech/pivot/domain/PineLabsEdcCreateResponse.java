package com.stpl.tech.pivot.domain;

import com.google.gson.annotations.SerializedName;
import com.stpl.tech.pivot.utils.ApplicationConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PineLabsEdcCreateResponse {

    @SerializedName("ResponseCode")
    private Integer responseCode;
    
    @SerializedName("ResponseMessage")
    private String responseMessage;
    
    @SerializedName("PlutusTransactionReferenceID")
    private Long plutusTransactionReferenceId;
    
    @SerializedName("AdditionalInfo")
    private List<PineLabsAdditionalInfo> additionalInfo;

    private String responseTime;

    public boolean isSuccess() {
        return ApplicationConstant.PINE_LABS_SUCCESS_CODE.equals(responseCode);
    }
    
    public boolean isApproved() {
        return ApplicationConstant.APPROVED.equalsIgnoreCase(responseMessage);
    }
}
