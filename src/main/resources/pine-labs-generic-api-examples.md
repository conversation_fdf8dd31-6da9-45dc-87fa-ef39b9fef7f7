# Pine Labs Generic API Examples

## 1. Generic Payment Status Check

### Endpoint: `POST /payment/status/check`

This generic endpoint works for all payment partners including Pine Labs EDC/DQR.

### Request Body:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "partnerTransactionId": "238165",
  "merchantId": "YOUR_MERCHANT_ID",
  "securityToken": "YOUR_SECURITY_TOKEN",
  "clientId": "YOUR_CLIENT_ID",
  "storeId": "YOUR_STORE_ID",
  "plutusTransactionReferenceId": 238165
}
```

### Response:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "partnerTransactionId": "238165",
  "partnerOrderId": "238165",
  "status": "TXN APPROVED",
  "message": "TXN APPROVED",
  "success": true,
  "responseCode": "0",
  "responseMessage": "TXN APPROVED",
  "amount": 100.00,
  "paymentMode": "CARD",
  "cardNumber": "416021******5719",
  "cardType": "VISA",
  "approvalCode": "00",
  "rrn": "000020",
  "batchNumber": "4",
  "invoiceNumber": "5",
  "acquirerId": "02",
  "acquirerName": "ICICI",
  "plutusTransactionReferenceId": 238165,
  "transactionData": [
    {
      "tag": "TID",
      "value": "01222221"
    },
    {
      "tag": "PaymentMode",
      "value": "CARD"
    },
    {
      "tag": "Amount",
      "value": "10000"
    }
  ],
  "responseTime": "2024-12-02T10:30:02.000Z"
}
```

## 2. Generic Payment Cancellation

### Endpoint: `POST /payment/transaction/cancel`

This generic endpoint works for all payment partners including Pine Labs EDC/DQR.

### Request Body:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "partnerTransactionId": "238165",
  "merchantId": "YOUR_MERCHANT_ID",
  "securityToken": "YOUR_SECURITY_TOKEN",
  "clientId": "YOUR_CLIENT_ID",
  "storeId": "YOUR_STORE_ID",
  "plutusTransactionReferenceId": 238165,
  "amount": 100.00,
  "reason": "Customer requested cancellation"
}
```

### Response:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "partnerTransactionId": "238165",
  "partnerOrderId": "238165",
  "status": "APPROVED",
  "message": "APPROVED",
  "success": true,
  "responseCode": "0",
  "responseMessage": "APPROVED",
  "amount": 100.00,
  "plutusTransactionReferenceId": 238165,
  "cancelTime": "2024-12-02T10:35:00.000Z",
  "cancelReason": "Customer requested cancellation",
  "responseTime": "2024-12-02T10:35:01.000Z"
}
```

## 3. Pine Labs DQR Status Check

### Request Body (for DQR):
```json
{
  "paymentPartnerType": "PINE_LABS_DQR",
  "transactionId": "KO20241202123457",
  "partnerTransactionId": "238166",
  "merchantId": "YOUR_MERCHANT_ID",
  "securityToken": "YOUR_SECURITY_TOKEN",
  "clientId": "YOUR_CLIENT_ID",
  "storeId": "YOUR_STORE_ID",
  "plutusTransactionReferenceId": 238166
}
```

## 4. Error Responses

### Invalid Merchant:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "success": false,
  "status": "INVALID MERCHANT",
  "message": "INVALID MERCHANT",
  "responseCode": "1",
  "responseMessage": "INVALID MERCHANT",
  "errorCode": "1",
  "errorMessage": "Invalid merchant credentials"
}
```

### Transaction In Progress:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "success": false,
  "status": "CANNOT CANCEL AS TRANSACTION IS IN PROGRESS",
  "message": "CANNOT CANCEL AS TRANSACTION IS IN PROGRESS",
  "responseCode": "1",
  "responseMessage": "CANNOT CANCEL AS TRANSACTION IS IN PROGRESS"
}
```

### Transaction Not Found:
```json
{
  "paymentPartnerType": "PINE_LABS_EDC",
  "transactionId": "KO20241202123456",
  "success": false,
  "status": "TRANSACTION NOT FOUND",
  "message": "TRANSACTION NOT FOUND",
  "responseCode": "1",
  "responseMessage": "TRANSACTION NOT FOUND"
}
```

## 5. Status Helper Methods

The response objects include helpful methods:

```java
// Check if transaction is approved
response.isApproved(); // true/false

// Check if transaction is pending
response.isPending(); // true/false

// Check if transaction failed
response.isFailed(); // true/false

// Check if transaction is uploaded (Pine Labs specific)
response.isUploaded(); // true/false

// Check if transaction is voided
response.isVoided(); // true/false

// Check if UPI transaction is initiated
response.isUpiInitiated(); // true/false

// Check payment partner type
response.isPineLabs(); // true/false
```

## 6. Integration Flow

1. **Initiate Transaction**: Use `/payment/initiate/transaction/pinelabs`
2. **Check Status**: Use `/payment/status/check` periodically
3. **Cancel if needed**: Use `/payment/transaction/cancel`

## 7. Configuration Required

```properties
# Pine Labs Configuration
pinelabs.base-url=https://www.plutuscloudserviceuat.in:8201
pinelabs.upload-transaction-api=/API/CloudBasedIntegration/V1/UploadBilledTransaction
pinelabs.get-status-api=/API/CloudBasedIntegration/V1/GetCloudBasedTxnStatus
pinelabs.cancel-transaction-api=/API/CloudBasedIntegration/V1/CancelTransaction
pinelabs.merchant-id=YOUR_MERCHANT_ID
pinelabs.security-token=YOUR_SECURITY_TOKEN
pinelabs.client-id=YOUR_CLIENT_ID
pinelabs.store-id=YOUR_STORE_ID
pinelabs.allowed-payment-mode=10
pinelabs.auto-cancel-duration-in-minutes=5
```
